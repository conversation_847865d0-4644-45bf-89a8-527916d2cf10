# 助力券锁定机制优化验证脚本

## 验证步骤

### 1. 基本功能验证

```kotlin
// 在 MainActivity 或测试类中添加以下代码进行验证

class BoostCouponLockVerification {
    
    fun verifyBasicLocking(context: Context) {
        Log.i("Verification", "=== 开始基本锁定功能验证 ===")
        
        // 1. 清理所有现有锁定
        val initialCleared = GameCodeManager.forceCleanupAllLocks()
        Log.i("Verification", "初始清理锁定数: $initialCleared")
        
        // 2. 获取初始统计
        val (initialTotal, initialSoonExpired) = GameCodeManager.getLockStatistics()
        Log.i("Verification", "初始锁定统计 - 总数: $initialTotal, 即将过期: $initialSoonExpired")
        
        // 3. 尝试获取游戏码
        val gameCode1 = GameCodeManager.getAndMarkGameCode(context)
        if (gameCode1 != null) {
            Log.i("Verification", "成功获取游戏码: ${gameCode1.prizeId}:${gameCode1.gameCode}")
            
            // 4. 检查锁定状态
            val (afterLockTotal, afterLockSoonExpired) = GameCodeManager.getLockStatistics()
            Log.i("Verification", "锁定后统计 - 总数: $afterLockTotal, 即将过期: $afterLockSoonExpired")
            
            // 5. 获取锁定详情
            val lockInfo = GameCodeManager.getLockStatusInfo()
            lockInfo.forEach { info ->
                Log.i("Verification", "锁定详情 - Key: ${info.key}, 剩余时间: ${info.remainingTime}ms")
            }
            
            // 6. 尝试重复获取相同游戏码（应该失败）
            val gameCode2 = GameCodeManager.getAndMarkGameCode(context)
            if (gameCode2 == null) {
                Log.i("Verification", "✓ 正确阻止了重复获取相同游戏码")
            } else {
                Log.e("Verification", "✗ 错误：允许了重复获取相同游戏码")
            }
            
            // 7. 释放游戏码
            val released = GameCodeManager.releaseGameCode(gameCode1.prizeId, gameCode1.gameCode)
            Log.i("Verification", "释放游戏码结果: $released")
            
            // 8. 检查释放后状态
            val (afterReleaseTotal, afterReleaseSoonExpired) = GameCodeManager.getLockStatistics()
            Log.i("Verification", "释放后统计 - 总数: $afterReleaseTotal, 即将过期: $afterReleaseSoonExpired")
            
        } else {
            Log.w("Verification", "无法获取游戏码，可能没有可用的游戏码文件")
        }
        
        Log.i("Verification", "=== 基本锁定功能验证完成 ===")
    }
    
    fun verifyMonitoringFeatures() {
        Log.i("Verification", "=== 开始监控功能验证 ===")
        
        // 1. 启动监控
        BoostCouponLockUtils.startSystemMonitoring(
            intervalMs = 5_000L,
            enableDetailedLog = true,
            autoRecover = false
        )
        
        // 2. 获取系统状态摘要
        val statusSummary = BoostCouponLockUtils.getSystemStatusSummary()
        Log.i("Verification", "系统状态摘要:\n$statusSummary")
        
        // 3. 执行健康检查
        val healthResult = BoostCouponLockUtils.checkAndRecoverSystem(autoRecover = false)
        Log.i("Verification", "健康检查结果:\n${healthResult.getFormattedReport()}")
        
        // 4. 获取诊断报告
        val diagnostics = BoostCouponLockUtils.performSystemDiagnostics()
        Log.i("Verification", "系统诊断报告:\n$diagnostics")
        
        // 5. 等待一段时间观察监控日志
        Thread.sleep(15_000) // 等待15秒
        
        // 6. 停止监控
        BoostCouponLockUtils.stopSystemMonitoring()
        
        Log.i("Verification", "=== 监控功能验证完成 ===")
    }
    
    fun verifyTimeoutAndRecovery(context: Context) {
        Log.i("Verification", "=== 开始超时和恢复功能验证 ===")
        
        // 1. 清理现有锁定
        GameCodeManager.forceCleanupAllLocks()
        
        // 2. 获取多个游戏码创建锁定
        val gameCodes = GameCodeManager.getAndMarkMultipleGameCodes(context, maxCount = 5)
        Log.i("Verification", "获取到 ${gameCodes.size} 个游戏码")
        
        if (gameCodes.isNotEmpty()) {
            // 3. 检查锁定状态
            val (total, soonExpired) = GameCodeManager.getLockStatistics()
            Log.i("Verification", "创建锁定后 - 总数: $total, 即将过期: $soonExpired")
            
            // 4. 模拟等待一段时间（在实际环境中，锁定会在15秒后自动释放）
            Log.i("Verification", "等待5秒观察锁定状态...")
            Thread.sleep(5_000)
            
            // 5. 再次检查状态
            val (total2, soonExpired2) = GameCodeManager.getLockStatistics()
            Log.i("Verification", "等待后 - 总数: $total2, 即将过期: $soonExpired2")
            
            // 6. 测试紧急恢复
            val clearedCount = BoostCouponLockUtils.emergencyRecovery()
            Log.i("Verification", "紧急恢复清理了 $clearedCount 个锁定")
            
            // 7. 验证清理结果
            val (finalTotal, finalSoonExpired) = GameCodeManager.getLockStatistics()
            Log.i("Verification", "恢复后 - 总数: $finalTotal, 即将过期: $finalSoonExpired")
            
            if (finalTotal == 0) {
                Log.i("Verification", "✓ 紧急恢复功能正常工作")
            } else {
                Log.e("Verification", "✗ 紧急恢复功能异常")
            }
        }
        
        Log.i("Verification", "=== 超时和恢复功能验证完成 ===")
    }
    
    fun verifySafeExecution(context: Context) {
        Log.i("Verification", "=== 开始安全执行功能验证 ===")
        
        // 使用新的安全执行方法
        runBlocking {
            val result = BoostCouponLockUtils.safeExecuteBoostCouponOperationWithTimeout(
                context = context,
                excludePhoneNumber = null,
                maxCount = 3,
                timeoutMs = 10_000L
            ) { gameCodeInfoList ->
                Log.i("Verification", "安全执行中，获得 ${gameCodeInfoList.size} 个游戏码")
                
                // 模拟处理每个游戏码
                gameCodeInfoList.forEach { gameCodeInfo ->
                    Log.i("Verification", "处理游戏码: ${gameCodeInfo.prizeId}:${gameCodeInfo.gameCode}")
                    // 模拟一些处理时间
                    delay(1000)
                }
                
                // 返回成功
                true
            }
            
            Log.i("Verification", "安全执行结果: $result")
            
            // 验证锁定已被自动释放
            val (finalTotal, _) = GameCodeManager.getLockStatistics()
            Log.i("Verification", "执行后剩余锁定数: $finalTotal")
            
            if (finalTotal == 0) {
                Log.i("Verification", "✓ 安全执行功能正常，锁定已自动释放")
            } else {
                Log.w("Verification", "⚠ 执行后仍有锁定，可能是其他操作产生的")
            }
        }
        
        Log.i("Verification", "=== 安全执行功能验证完成 ===")
    }
    
    fun runAllVerifications(context: Context) {
        Log.i("Verification", "开始完整的助力券锁定机制验证")
        
        try {
            verifyBasicLocking(context)
            Thread.sleep(2000)
            
            verifyMonitoringFeatures()
            Thread.sleep(2000)
            
            verifyTimeoutAndRecovery(context)
            Thread.sleep(2000)
            
            verifySafeExecution(context)
            
            Log.i("Verification", "✓ 所有验证完成")
            
        } catch (e: Exception) {
            Log.e("Verification", "验证过程中发生异常", e)
        }
    }
}
```

## 使用方法

### 在 Activity 中调用验证

```kotlin
class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        // 在开发/测试环境中运行验证
        if (BuildConfig.DEBUG) {
            Thread {
                val verification = BoostCouponLockVerification()
                verification.runAllVerifications(this)
            }.start()
        }
    }
}
```

### 预期的日志输出

正常情况下，您应该看到类似以下的日志输出：

```
I/Verification: === 开始基本锁定功能验证 ===
I/Verification: 初始清理锁定数: 0
I/Verification: 初始锁定统计 - 总数: 0, 即将过期: 0
I/Verification: 成功获取游戏码: prize123:game123
I/Verification: 锁定后统计 - 总数: 1, 即将过期: 0
I/Verification: 锁定详情 - Key: prize123:game123, 剩余时间: 14995ms
I/Verification: ✓ 正确阻止了重复获取相同游戏码
I/Verification: 释放游戏码结果: true
I/Verification: 释放后统计 - 总数: 0, 即将过期: 0
I/Verification: === 基本锁定功能验证完成 ===
```

## 故障排除

### 如果验证失败

1. **检查游戏码文件**：确保 `gameCodeList.txt` 文件存在且包含有效数据
2. **检查权限**：确保应用有文件读写权限
3. **查看详细日志**：启用详细日志模式查看更多信息
4. **检查线程安全**：确保没有其他代码同时操作游戏码

### 常见问题

1. **"无法获取游戏码"**：检查游戏码文件是否存在和格式是否正确
2. **"锁定未自动释放"**：检查清理任务是否正常运行
3. **"监控功能异常"**：检查协程和线程池是否正常工作

## 性能测试

可以通过以下方式测试性能：

```kotlin
fun performanceTest(context: Context) {
    val startTime = System.currentTimeMillis()
    
    // 测试大量锁定操作
    repeat(100) {
        val gameCode = GameCodeManager.getAndMarkGameCode(context)
        gameCode?.let {
            GameCodeManager.releaseGameCode(it.prizeId, it.gameCode)
        }
    }
    
    val endTime = System.currentTimeMillis()
    Log.i("Performance", "100次锁定/释放操作耗时: ${endTime - startTime}ms")
}
```

这个验证脚本将帮助您确认所有优化功能都正常工作。
