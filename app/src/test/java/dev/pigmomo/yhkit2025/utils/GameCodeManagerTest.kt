package dev.pigmomo.yhkit2025.utils

import android.content.Context
import org.junit.Test
import org.junit.Before
import org.junit.After
import org.mockito.Mockito.*
import org.junit.Assert.*
import java.io.File
import java.util.concurrent.TimeUnit

/**
 * GameCodeManager 的单元测试
 * 测试新的锁定机制和超时功能
 */
class GameCodeManagerTest {
    
    private lateinit var mockContext: Context
    private lateinit var testFile: File
    
    @Before
    fun setUp() {
        mockContext = mock(Context::class.java)
        testFile = File.createTempFile("gameCodeList", ".txt")
        
        // 模拟 Context.getFilesDir() 返回临时目录
        `when`(mockContext.filesDir).thenReturn(testFile.parentFile)
        
        // 清理所有锁定状态
        GameCodeManager.forceCleanupAllLocks()
    }
    
    @After
    fun tearDown() {
        // 清理测试文件
        testFile.delete()
        
        // 清理所有锁定状态
        GameCodeManager.forceCleanupAllLocks()
    }
    
    @Test
    fun testLockTimeout() {
        // 创建测试游戏码文件
        val testGameCode = """
            2024-01-01 12:00:00,13800000001,prize123,game123
        """.trimIndent()
        testFile.writeText(testGameCode)
        
        // 获取并标记游戏码
        val gameCodeInfo = GameCodeManager.getAndMarkGameCode(mockContext)
        assertNotNull("应该能获取到游戏码", gameCodeInfo)
        
        // 验证锁定状态
        val (totalLocks, _) = GameCodeManager.getLockStatistics()
        assertEquals("应该有1个锁定", 1, totalLocks)
        
        // 等待超过锁定超时时间（这里我们不能等待15秒，所以我们测试清理功能）
        // 手动触发清理（在实际测试中，我们可能需要修改超时时间为更短的值）
        
        // 验证锁定信息
        val lockInfoList = GameCodeManager.getLockStatusInfo()
        assertEquals("应该有1个锁定信息", 1, lockInfoList.size)
        assertTrue("剩余时间应该大于0", lockInfoList[0].remainingTime > 0)
    }
    
    @Test
    fun testForceCleanupAllLocks() {
        // 创建测试游戏码文件
        val testGameCode = """
            2024-01-01 12:00:00,13800000001,prize123,game123
            2024-01-01 12:01:00,13800000002,prize456,game456
        """.trimIndent()
        testFile.writeText(testGameCode)
        
        // 获取并标记多个游戏码
        val gameCodeInfo1 = GameCodeManager.getAndMarkGameCode(mockContext)
        val gameCodeInfo2 = GameCodeManager.getAndMarkGameCode(mockContext, "13800000001")
        
        assertNotNull("应该能获取到第一个游戏码", gameCodeInfo1)
        assertNotNull("应该能获取到第二个游戏码", gameCodeInfo2)
        
        // 验证锁定状态
        val (totalLocks, _) = GameCodeManager.getLockStatistics()
        assertEquals("应该有2个锁定", 2, totalLocks)
        
        // 强制清理所有锁定
        val clearedCount = GameCodeManager.forceCleanupAllLocks()
        assertEquals("应该清理2个锁定", 2, clearedCount)
        
        // 验证清理后的状态
        val (totalLocksAfter, _) = GameCodeManager.getLockStatistics()
        assertEquals("清理后应该没有锁定", 0, totalLocksAfter)
    }
    
    @Test
    fun testGetAndMarkMultipleGameCodes() {
        // 创建测试游戏码文件
        val testGameCode = """
            2024-01-01 12:00:00,13800000001,prize123,game123
            2024-01-01 12:01:00,13800000002,prize456,game456
            2024-01-01 12:02:00,13800000003,prize789,game789
        """.trimIndent()
        testFile.writeText(testGameCode)
        
        // 获取并标记多个游戏码
        val gameCodeInfoList = GameCodeManager.getAndMarkMultipleGameCodes(mockContext, maxCount = 3)
        
        assertEquals("应该获取到3个游戏码", 3, gameCodeInfoList.size)
        
        // 验证锁定状态
        val (totalLocks, _) = GameCodeManager.getLockStatistics()
        assertEquals("应该有3个锁定", 3, totalLocks)
        
        // 验证不能重复获取相同的游戏码
        val gameCodeInfoList2 = GameCodeManager.getAndMarkMultipleGameCodes(mockContext, maxCount = 3)
        assertEquals("不应该能获取到已锁定的游戏码", 0, gameCodeInfoList2.size)
    }
    
    @Test
    fun testReleaseGameCode() {
        // 创建测试游戏码文件
        val testGameCode = """
            2024-01-01 12:00:00,13800000001,prize123,game123
        """.trimIndent()
        testFile.writeText(testGameCode)
        
        // 获取并标记游戏码
        val gameCodeInfo = GameCodeManager.getAndMarkGameCode(mockContext)
        assertNotNull("应该能获取到游戏码", gameCodeInfo)
        
        // 验证锁定状态
        val (totalLocks, _) = GameCodeManager.getLockStatistics()
        assertEquals("应该有1个锁定", 1, totalLocks)
        
        // 释放游戏码
        val released = GameCodeManager.releaseGameCode(gameCodeInfo!!.prizeId, gameCodeInfo.gameCode)
        assertTrue("应该能成功释放游戏码", released)
        
        // 验证释放后的状态
        val (totalLocksAfter, _) = GameCodeManager.getLockStatistics()
        assertEquals("释放后应该没有锁定", 0, totalLocksAfter)
        
        // 验证可以重新获取相同的游戏码
        val gameCodeInfo2 = GameCodeManager.getAndMarkGameCode(mockContext)
        assertNotNull("应该能重新获取游戏码", gameCodeInfo2)
        assertEquals("应该是相同的游戏码", gameCodeInfo.gameCode, gameCodeInfo2!!.gameCode)
    }
    
    @Test
    fun testLockStatistics() {
        // 初始状态应该没有锁定
        val (initialTotal, initialSoonExpired) = GameCodeManager.getLockStatistics()
        assertEquals("初始状态应该没有锁定", 0, initialTotal)
        assertEquals("初始状态应该没有即将过期的锁定", 0, initialSoonExpired)
        
        // 创建测试游戏码文件
        val testGameCode = """
            2024-01-01 12:00:00,13800000001,prize123,game123
            2024-01-01 12:01:00,13800000002,prize456,game456
        """.trimIndent()
        testFile.writeText(testGameCode)
        
        // 获取并标记游戏码
        GameCodeManager.getAndMarkGameCode(mockContext)
        GameCodeManager.getAndMarkGameCode(mockContext, "13800000001")
        
        // 验证统计信息
        val (total, soonExpired) = GameCodeManager.getLockStatistics()
        assertEquals("应该有2个锁定", 2, total)
        assertEquals("新锁定不应该即将过期", 0, soonExpired)
    }
    
    @Test
    fun testGetLockStatusInfo() {
        // 创建测试游戏码文件
        val testGameCode = """
            2024-01-01 12:00:00,13800000001,prize123,game123
        """.trimIndent()
        testFile.writeText(testGameCode)
        
        // 获取并标记游戏码
        val gameCodeInfo = GameCodeManager.getAndMarkGameCode(mockContext)
        assertNotNull("应该能获取到游戏码", gameCodeInfo)
        
        // 获取锁定状态信息
        val lockInfoList = GameCodeManager.getLockStatusInfo()
        assertEquals("应该有1个锁定信息", 1, lockInfoList.size)
        
        val lockInfo = lockInfoList[0]
        assertEquals("锁定键应该正确", "prize123:game123", lockInfo.key)
        assertTrue("剩余时间应该大于0", lockInfo.remainingTime > 0)
        assertTrue("剩余时间应该小于等于15秒", lockInfo.remainingTime <= 15_000L)
    }
}
